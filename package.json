{"name": "omni-webui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "biome check", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "highlight.js": "^11.11.1", "lucide-react": "^0.484.0", "marked": "^15.0.7", "nanoid": "^5.1.5", "openai": "^4.90.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-mathjax": "^7.1.0", "remark-math": "^6.0.0", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/marked": "^6.0.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.5.0", "jsdom": "^24.0.0", "tailwindcss": "^4.0.16", "typescript": "^5", "vite": "^6.2.3", "vite-plugin-biome": "^1.0.12", "vitest": "^1.5.0"}}